<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MessagePack</name>
    </assembly>
    <members>
        <member name="T:MessagePack.Formatters.NativeDateTimeFormatter">
            <summary>
            Serialize by .NET native DateTime binary format.
            </summary>
        </member>
        <member name="T:MessagePack.Formatters.OldSpecStringFormatter">
            <summary>
            Old-MessagePack spec's string formatter.
            </summary>
        </member>
        <member name="T:MessagePack.Formatters.OldSpecBinaryFormatter">
            <summary>
            Old-MessagePack spec's binary formatter.
            </summary>
        </member>
        <member name="T:MessagePack.Internal.ILGeneratorExtensions">
            <summary>
            Provides optimized generation code and helpers.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ILGeneratorExtensions.EmitLdloc(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Loads the local variable at a specific index onto the evaluation stack.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ILGeneratorExtensions.EmitStloc(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Pops the current value from the top of the evaluation stack and stores it in a the local variable list at a specified index.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ILGeneratorExtensions.EmitLdloca(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Loads the address of the local variable at a specific index onto the evaluation statck.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ILGeneratorExtensions.EmitLdc_I4(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Pushes a supplied value of type int32 onto the evaluation stack as an int32.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ILGeneratorExtensions.EmitPop(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Helper for Pop op.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ILGeneratorExtensions.EmitIncrementFor(System.Reflection.Emit.ILGenerator,System.Reflection.Emit.LocalBuilder,System.Action{System.Reflection.Emit.LocalBuilder})">
            <summary>for  var i = 0, i ..., i++ </summary>
        </member>
        <member name="T:MessagePack.LZ4.LZ4Codec">
            <summary>Safe LZ4 codec.</summary>
            <summary>Unsafe LZ4 codec.</summary>
        </member>
        <member name="F:MessagePack.LZ4.LZ4Codec.MEMORY_USAGE">
            <summary>
            Memory usage formula : N->2^N Bytes (examples : 10 -> 1KB; 12 -> 4KB ; 16 -> 64KB; 20 -> 1MB; etc.)
            Increasing memory usage improves compression ratio
            Reduced memory usage can improve speed, due to cache effect
            Default value is 14, for 16KB, which nicely fits into Intel x86 L1 cache
            </summary>
        </member>
        <member name="F:MessagePack.LZ4.LZ4Codec.NOTCOMPRESSIBLE_DETECTIONLEVEL">
            <summary>
            Decreasing this value will make the algorithm skip faster data segments considered "incompressible"
            This may decrease compression ratio dramatically, but will be faster on incompressible data
            Increasing this value will make the algorithm search more before declaring a segment "incompressible"
            This could improve compression a bit, but will be slower on incompressible data
            The default value (6) is recommended
            </summary>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.MaximumOutputLength(System.Int32)">
            <summary>Gets maximum the length of the output.</summary>
            <param name="inputLength">Length of the input.</param>
            <returns>Maximum number of bytes needed for compressed buffer.</returns>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.Encode32Safe(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32)">
            <summary>Encodes the specified input.</summary>
            <param name="input">The input.</param>
            <param name="inputOffset">The input offset.</param>
            <param name="inputLength">Length of the input.</param>
            <param name="output">The output.</param>
            <param name="outputOffset">The output offset.</param>
            <param name="outputLength">Length of the output.</param>
            <returns>Number of bytes written.</returns>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.Encode64Safe(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32)">
            <summary>Encodes the specified input.</summary>
            <param name="input">The input.</param>
            <param name="inputOffset">The input offset.</param>
            <param name="inputLength">Length of the input.</param>
            <param name="output">The output.</param>
            <param name="outputOffset">The output offset.</param>
            <param name="outputLength">Length of the output.</param>
            <returns>Number of bytes written.</returns>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.Decode32Safe(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32)">
            <summary>Decodes the specified input.</summary>
            <param name="input">The input.</param>
            <param name="inputOffset">The input offset.</param>
            <param name="inputLength">Length of the input.</param>
            <param name="output">The output.</param>
            <param name="outputOffset">The output offset.</param>
            <param name="outputLength">Length of the output.</param>
            <returns>Number of bytes written.</returns>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.Decode64Safe(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32)">
            <summary>Decodes the specified input.</summary>
            <param name="input">The input.</param>
            <param name="inputOffset">The input offset.</param>
            <param name="inputLength">Length of the input.</param>
            <param name="output">The output.</param>
            <param name="outputOffset">The output offset.</param>
            <param name="outputLength">Length of the output.</param>
            <returns>Number of bytes written.</returns>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.BlockCopy(System.Byte*,System.Byte*,System.Int32)">
            <summary>Copies block of memory.</summary>
            <param name="src">The source.</param>
            <param name="dst">The destination.</param>
            <param name="len">The length (in bytes).</param>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.Encode32Unsafe(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32)">
            <summary>Encodes the specified input.</summary>
            <param name="input">The input.</param>
            <param name="inputOffset">The input offset.</param>
            <param name="inputLength">Length of the input.</param>
            <param name="output">The output.</param>
            <param name="outputOffset">The output offset.</param>
            <param name="outputLength">Length of the output.</param>
            <returns>Number of bytes written.</returns>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.Decode32Unsafe(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32)">
            <summary>Decodes the specified input.</summary>
            <param name="input">The input.</param>
            <param name="inputOffset">The input offset.</param>
            <param name="inputLength">Length of the input.</param>
            <param name="output">The output.</param>
            <param name="outputOffset">The output offset.</param>
            <param name="outputLength">Length of the output.</param>
            <returns>Number of bytes written.</returns>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.Encode64Unsafe(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32)">
            <summary>Encodes the specified input.</summary>
            <param name="input">The input.</param>
            <param name="inputOffset">The input offset.</param>
            <param name="inputLength">Length of the input.</param>
            <param name="output">The output.</param>
            <param name="outputOffset">The output offset.</param>
            <param name="outputLength">Length of the output.</param>
            <returns>Number of bytes written.</returns>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.Decode64Unsafe(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32)">
            <summary>Decode64s the specified input.</summary>
            <param name="input">The input.</param>
            <param name="inputOffset">The input offset.</param>
            <param name="inputLength">Length of the input.</param>
            <param name="output">The output.</param>
            <param name="outputOffset">The output offset.</param>
            <param name="outputLength">Length of the output.</param>
            <returns>Number of bytes written.</returns>
        </member>
        <member name="T:MessagePack.LZ4MessagePackSerializer">
            <summary>
            LZ4 Compressed special serializer.
            </summary>
        </member>
        <member name="M:MessagePack.LZ4MessagePackSerializer.Serialize``1(``0)">
            <summary>
            Serialize to binary with default resolver.
            </summary>
        </member>
        <member name="M:MessagePack.LZ4MessagePackSerializer.Serialize``1(``0,MessagePack.IFormatterResolver)">
            <summary>
            Serialize to binary with specified resolver.
            </summary>
        </member>
        <member name="M:MessagePack.LZ4MessagePackSerializer.Serialize``1(System.IO.Stream,``0)">
            <summary>
            Serialize to stream.
            </summary>
        </member>
        <member name="M:MessagePack.LZ4MessagePackSerializer.Serialize``1(System.IO.Stream,``0,MessagePack.IFormatterResolver)">
            <summary>
            Serialize to stream with specified resolver.
            </summary>
        </member>
        <member name="M:MessagePack.LZ4MessagePackSerializer.ToJson``1(``0)">
            <summary>
            Dump to JSON string.
            </summary>
        </member>
        <member name="M:MessagePack.LZ4MessagePackSerializer.ToJson``1(``0,MessagePack.IFormatterResolver)">
            <summary>
            Dump to JSON string.
            </summary>
        </member>
        <member name="M:MessagePack.LZ4MessagePackSerializer.ToJson(System.Byte[])">
            <summary>
            Dump message-pack binary to JSON string.
            </summary>
        </member>
        <member name="T:MessagePack.MessagePackBinary">
            <summary>
            Encode/Decode Utility of MessagePack Spec.
            https://github.com/msgpack/msgpack/blob/master/spec.md
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.WriteFixedMapHeaderUnsafe(System.Byte[]@,System.Int32,System.Int32)">
            <summary>
            Unsafe. If value is guranteed 0 ~ MessagePackRange.MaxFixMapCount(15), can use this method.
            </summary>
            <returns></returns>
        </member>
        <member name="M:MessagePack.MessagePackBinary.WriteMapHeader(System.Byte[]@,System.Int32,System.Int32)">
            <summary>
            Write map count.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.WriteMapHeader(System.Byte[]@,System.Int32,System.UInt32)">
            <summary>
            Write map count.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.ReadMapHeader(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Return map count.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.ReadMapHeaderRaw(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Return map count.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.WriteFixedArrayHeaderUnsafe(System.Byte[]@,System.Int32,System.Int32)">
            <summary>
            Unsafe. If value is guranteed 0 ~ MessagePackRange.MaxFixArrayCount(15), can use this method.
            </summary>
            <returns></returns>
        </member>
        <member name="M:MessagePack.MessagePackBinary.WriteArrayHeader(System.Byte[]@,System.Int32,System.Int32)">
            <summary>
            Write array count.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.WriteArrayHeader(System.Byte[]@,System.Int32,System.UInt32)">
            <summary>
            Write array count.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.ReadArrayHeader(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Return array count.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.ReadArrayHeaderRaw(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            Return array count.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.WritePositiveFixedIntUnsafe(System.Byte[]@,System.Int32,System.Int32)">
            <summary>
            Unsafe. If value is guranteed 0 ~ MessagePackCode.MaxFixInt(127), can use this method.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.WriteInt32ForceInt32Block(System.Byte[]@,System.Int32,System.Int32)">
            <summary>
            Acquire static message block(always 5 bytes).
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.WriteFixedStringUnsafe(System.Byte[]@,System.Int32,System.String,System.Int32)">
            <summary>
            Unsafe. If value is guranteed length is 0 ~ 31, can use this method.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.WriteStringUnsafe(System.Byte[]@,System.Int32,System.String,System.Int32)">
            <summary>
            Unsafe. If pre-calculated byteCount of target string, can use this method.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.WriteExtensionFormatHeaderForceExt32Block(System.Byte[]@,System.Int32,System.SByte,System.Int32)">
            <summary>
            Write extension format header, always use ext32 format(length is fixed, 6).
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackBinary.ReadExtensionFormatHeader(System.Byte[],System.Int32,System.Int32@)">
            <summary>
            return byte length of ExtensionFormat.
            </summary>
        </member>
        <member name="T:MessagePack.MessagePackType">
            <summary>
            https://github.com/msgpack/msgpack/blob/master/spec.md#serialization-type-to-format-conversion
            </summary>
        </member>
        <member name="T:MessagePack.MessagePackCode">
            <summary>
            https://github.com/msgpack/msgpack/blob/master/spec.md#overview
            </summary>
        </member>
        <member name="T:MessagePack.MessagePackSerializer">
            <summary>
            High-Level API of MessagePack for C#.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackSerializer.DefaultResolver">
            <summary>
            FormatterResolver that used resolver less overloads. If does not set it, used StandardResolver.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackSerializer.IsInitialized">
            <summary>
            Is resolver decided?
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.SetDefaultResolver(MessagePack.IFormatterResolver)">
            <summary>
            Set default resolver of MessagePackSerializer APIs.
            </summary>
            <param name="resolver"></param>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Serialize``1(``0)">
            <summary>
            Serialize to binary with default resolver.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Serialize``1(``0,MessagePack.IFormatterResolver)">
            <summary>
            Serialize to binary with specified resolver.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.SerializeUnsafe``1(``0)">
            <summary>
            Serialize to binary. Get the raw memory pool byte[]. The result can not share across thread and can not hold, so use quickly.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.SerializeUnsafe``1(``0,MessagePack.IFormatterResolver)">
            <summary>
            Serialize to binary with specified resolver. Get the raw memory pool byte[]. The result can not share across thread and can not hold, so use quickly.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Serialize``1(System.IO.Stream,``0)">
            <summary>
            Serialize to stream.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Serialize``1(System.IO.Stream,``0,MessagePack.IFormatterResolver)">
            <summary>
            Serialize to stream with specified resolver.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.ToJson``1(``0)">
            <summary>
            Dump to JSON string.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.ToJson``1(``0,MessagePack.IFormatterResolver)">
            <summary>
            Dump to JSON string.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.ToJson(System.Byte[])">
            <summary>
            Dump message-pack binary to JSON string.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.DynamicEnumResolver">
            <summary>
            EnumResolver by dynamic code generation, serialized underlying type.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.DynamicObjectResolver">
            <summary>
            ObjectResolver by dynamic code generation.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.DynamicContractlessObjectResolver">
            <summary>
            ObjectResolver by dynamic code generation, no needs MessagePackObject attribute and serialized key as string.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.DynamicUnionResolver">
            <summary>
            UnionResolver by dynamic code generation.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.StandardResolver">
            <summary>
            Default composited resolver, builtin -> dynamic enum -> dynamic generic -> dynamic union -> dynamic object.
            </summary>
        </member>
    </members>
</doc>
