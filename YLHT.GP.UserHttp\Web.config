﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  有关如何配置 ASP.NET 应用程序的详细信息，请访问
  https://go.microsoft.com/fwlink/?LinkId=169433
-->
<configuration>

  <connectionStrings>
    <!--<add name="ConnectionString" connectionString="Data Source=orcl_************;User Id=ylht;Password=********;"/>-->
  </connectionStrings>
  
  <appSettings>
    <!--此参数为必需参数-->
    <!--<add key="AppId" value="1"/>
    <add key="AppName" value="YLHT.GP.UserHttp"/>
    <add key="LogPath" value="E:\YLHT.GP\Log"/>
    <add key="BindIP" value="127.0.0.1,************,************,************,************,************23,************92,************,************,************" />
    <add key="BufferAppender" value="true" />
    <add key="ConsoleAppender" value="false"/>
    <add key="BufferInterval" value="5000"/>
    <add key="DataSourceUrl" value="YLHT.GP.OracleData.DataSource, YLHT.GP.OracleData, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <add key="BillingUrl" value="tcp://127.0.0.1:50001/Billing"/>
    <add key="DataCenterUrl" value="tcp://127.0.0.1:50100/DataCenter"/>
    <add key="InterfaceFilePath" value="E:\YLHT.GP\InterfaceObject"/>
	<add key="IsKafkaLog" value="2"/>
    --><!--<add key="InterfaceQueuePath" value="FormatName:DIRECT=TCP:************\private$\YLHT_GP_Core_Interface"/>--><!--
    <add key="InterfaceQueuePath" value="FormatName:DIRECT=TCP:127.0.0.1\private$\YLHT_GP_Core_Interface"/>
    <add key="MmsDataFilePath" value="E:\YLHT.GP\MmsDataObject"/>
    <add key="InterfaceFilePath20" value="E:\YLHT.GP\InterfaceObject20"/>
    <add key="InterfaceQueuePath20" value="FormatName:DIRECT=TCP:127.0.0.1\private$\YLHT_GP_Core_Interface20"/>-->
  
  
  <add key="AppId" value="1" />
    <add key="AppName" value="YLHT.GP.UserHttp" />
    <add key="LogPath" value="D:\YLHT.GP1\Log" />
    <add key="BindIP" value="127.0.0.1,************,************,************,************,************23,************92,************,************,************" />
    <add key="BufferAppender" value="true" />
    <add key="ConsoleAppender" value="false" />
    <add key="BufferInterval" value="5000" />
    <add key="DataSourceUrl" value="YLHT.GP.OracleData.DataSource, YLHT.GP.OracleData, Version=*******, Culture=neutral, PublicKeyToken=null" />
    <add key="BillingUrl" value="tcp://127.0.0.1:50001/Billing" />
    <add key="DataCenterUrl" value="tcp://127.0.0.1:50100/DataCenter" />
    <add key="InterfaceFilePath" value="D:\YLHT.GP1\InterfaceObject" />
	<add key="IsKafkaLog" value="2" />
    <!--<add key="InterfaceQueuePath" value="FormatName:DIRECT=TCP:************\private$\YLHT_GP_Core_Interface"/>-->
    <add key="InterfaceQueuePath" value="FormatName:DIRECT=TCP:127.0.0.1\private$\YLHT_GP_Core_Interface" />
    <add key="MmsDataFilePath" value="D:\YLHT.GP1\MmsDataObject" />
    <add key="InterfaceFilePath20" value="D:\YLHT.GP1\InterfaceObject20" />
    <add key="InterfaceQueuePath20" value="FormatName:DIRECT=TCP:127.0.0.1\private$\YLHT_GP_Core_Interface20" />
    <add key="SubmitMsisdnCount" value="3000" />
  </appSettings>
  
  <system.web>
    <compilation debug="true" targetFramework="4.5" />
    <httpRuntime targetFramework="4.5" />
  </system.web>
  
  <runtime>
  
       <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
  
            <dependentAssembly>
  
                 <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
  
                 <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
  
            </dependentAssembly>
  
            <dependentAssembly>
  
                 <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
  
                 <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
  
            </dependentAssembly>
  
            <dependentAssembly>
  
                 <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
  
                 <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
  
            </dependentAssembly>
  
            <dependentAssembly>
  
                 <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
  
                 <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
  
            </dependentAssembly>
  
       </assemblyBinding>
  
  </runtime>
</configuration>