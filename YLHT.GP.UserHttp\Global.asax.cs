﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Security;
using System.Web.SessionState;
using YLHT.GP.Common;
using YLHT.GP.Common.Enums;
using YLHT.GP.Common.Host;
using YLHT.GP.Common.Log;
using YLHT.GP.Common.Utility;

namespace YLHT.GP.UserHttp
{
    public class Global : System.Web.HttpApplication
    {

        public override void Init()
        {
            base.Init();
        }

        protected void Application_Start(object sender, EventArgs e)
        {
            lock (this)
            {
                App.SetLogInfo();
                //LicenseHelper license = new LicenseHelper();
                //if (!license.checkCode())
                //{
                //    LogHelper.Warn("程序未授权！");
                //}
                //else
                //{
                    App.SetConfig(ConfigType.AppName, "YLHT.GP.UserHttp");
                    App.SetConfig(ConfigType.ResetEvents, new List<ResetEvent> {
                    ResetEvent.ResetUserEvent,
                    ResetEvent.ResetAdminEvent,
                    ResetEvent.ResetSignEvent,
                    ResetEvent.ResetKeyWordEvent,
                    ResetEvent.ResetProductChannelEvent,
                    ResetEvent.ResetNdcEvent,
                    ResetEvent.ResetNdcMacEvent,
                    ResetEvent.ResetChannelEvent,
                    ResetEvent.ResetVoiceSmsTemplateEvent,
                    ResetEvent.ResetVoiceTemplateEvent
                });
                //new DataCache(new List<ResetEvent> {
                //    ResetEvent.ResetUserEvent,
                //    ResetEvent.ResetAdminEvent,
                //    ResetEvent.ResetSignEvent,
                //    ResetEvent.ResetKeyWordEvent,
                //    ResetEvent.ResetProductChannelEvent,
                //    ResetEvent.ResetNdcEvent,
                //    ResetEvent.ResetNdcMacEvent,
                //    ResetEvent.ResetChannelEvent);
                Task.Run(() =>
                {
                    LogHelper.Info("Task创建计数器");
                    Counters.CreateCounter();
                });
                App.RegisterRemoting(1000 * 3);
                //    LogHelper.Info("应用程序启动完成");
                //}
            }
        }

        protected void Session_Start(object sender, EventArgs e)
        {

        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {

        }

        protected void Application_AuthenticateRequest(object sender, EventArgs e)
        {

        }

        protected void Application_Error(object sender, EventArgs e)
        {

        }

        protected void Session_End(object sender, EventArgs e)
        {

        }

        protected void Application_End(object sender, EventArgs e)
        {
            lock (this)
            {
                App.Dispose();
            }
        }
    }
}