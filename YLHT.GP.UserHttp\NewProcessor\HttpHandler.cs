﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Messaging;
using System.Web;
using YLHT.GP.Common;
using YLHT.GP.Common.DataSource;
using YLHT.GP.Common.Enums;
using YLHT.GP.Common.Host;
using YLHT.GP.Common.Log;
using YLHT.GP.Common.Modes;
using YLHT.GP.Common.Modules.Billing;
using YLHT.GP.Common.Objects;
using YLHT.GP.Common.Objects.UserHttp;
using YLHT.GP.Common.Objetcs20;

namespace YLHT.GP.UserHttp.NewProcessor
{
    /// <summary>
    /// 
    /// </summary>
    public static partial class HttpHandler
    {
        #region DataCache

        /// <summary>
        /// 
        /// </summary>
        private static Lazy<IDataCache> dataCache = new Lazy<IDataCache>(() => { return App.GetService<IDataCache>(); });

        /// <summary>
        /// 缓存对象
        /// </summary>
        private static IDataCache DataCache
        {
            get
            {
                return dataCache.Value;
            }
        }

        #endregion

        #region BillingCenter

        /// <summary>
        /// 延迟加载计费中心
        /// </summary>
        private static Lazy<IBillingCenter> billingCenter = new Lazy<IBillingCenter>(() => { return App.GetService<IBillingCenter>(); });

        /// <summary>
        /// 计费中心
        /// </summary>
        private static IBillingCenter BillingCenter
        {
            get
            {
                return billingCenter.Value;
            }
        }

        #endregion

        #region DataSource

        /// <summary>
        /// 延迟加载数据源
        /// </summary>
        private static Lazy<IDataSource> dataSource = new Lazy<IDataSource>(() => { return App.GetService<IDataSource>(); });

        /// <summary>
        /// 数据源
        /// </summary>
        private static IDataSource DataSource
        {
            get
            {
                return dataSource.Value;
            }
        }

        #endregion

        ///// <summary>
        ///// 
        ///// </summary>
        //private static string mmsDataFilePath;

        ///// <summary>
        ///// 表示彩信对象序列化时的存储路径。
        ///// </summary>
        //private static string MmsDataFilePath
        //{
        //    get
        //    {
        //        if (string.IsNullOrEmpty(mmsDataFilePath) == false)
        //        {
        //            return mmsDataFilePath;
        //        }
        //        var path = App.GetSetting("MmsDataFilePath");
        //        if (string.IsNullOrEmpty(path))
        //        {
        //            path = @"D:\YLHT\YLHT.GP\MmsDataFilePath";
        //        }
        //        mmsDataFilePath = path;
        //        return mmsDataFilePath;
        //    }
        //}

        /// <summary>
        /// 
        /// </summary>
        private static string interfaceFilePath20;

        /// <summary>
        /// 表示<see cref="InterfaceObject"/>对象序列化时的存储路径。
        /// </summary>
        private static string InterfaceFilePath20
        {
            get
            {
                if (string.IsNullOrEmpty(interfaceFilePath20) == false)
                {
                    return interfaceFilePath20;
                }
                var path = App.GetSetting("InterfaceFilePath20");
                if (string.IsNullOrEmpty(path))
                {
                    path = @"D:\YLHT\YLHT.GP\InterfaceObject20";
                }
                interfaceFilePath20 = path;
                return interfaceFilePath20;
            }
        }
        private static int  submitMsisdnCount;
        private static int SubmitMsisdnCount
        {
            get
            {
                if (submitMsisdnCount>0)
                {
                    return submitMsisdnCount;
                }
                var count = App.GetSetting("SubmitMsisdnCount");

                if (string.IsNullOrEmpty(count))
                {
                    try
                    {
                        var count1= Convert.ToInt32(count);
                        if (count1>0)
                        {
                            submitMsisdnCount = count1;
                            return submitMsisdnCount;
                        }
                        else
                        {
                            submitMsisdnCount = 3000;
                            return submitMsisdnCount;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Error("SubmitMsisdnCount转换异常，ex="+ex);
                        submitMsisdnCount = 3000;
                    }
                }
                else
                {
                    submitMsisdnCount = 3000;
                }
                return submitMsisdnCount;
            }
        }

        /// <summary>
        /// 表示<see cref="InterfaceObject"/>对象的消息队列路径
        /// </summary>
        private static string InterfaceQueuePath20
        {
            get { return App.GetSetting("InterfaceQueuePath20"); }
        }

        /// <summary>
        /// 延迟加载消息队列池
        /// </summary>
        private static Lazy<MessageQueue> queue = new Lazy<MessageQueue>(() =>
        {
            string path = InterfaceQueuePath20;
            if (string.IsNullOrEmpty(path))
            {
                path = Consts.InterfaceQueuePath20;
            }
            return Utils.GetMessageQueue(path, new XmlMessageFormatter(new[] { typeof(InterfaceObject20) }));
        });

        /// <summary>
        /// 获取消息队列对象
        /// </summary>
        private static MessageQueue Queue
        {
            get
            {
                return queue.Value;
            }
        }

        /// <summary>
        /// 将接口对象发送到消息队列
        /// </summary>
        /// <param name="interfaceObject"></param>
        private static void SendInterfaceObject(InterfaceObject20 interfaceObject)
        {
            Queue.SendObject(interfaceObject, (MessagePriority)(int)interfaceObject.Priority);//使用消息队列优先级功能
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="param"></param>
        /// <param name="timestmp"></param>
        /// <param name="platform"></param>
        /// <returns></returns>
        private static Result AccessFilter(YLHT.GP.UserHttp.Objects.MsgParam param, long timestmp, bool platform)
        {
            if (platform)
            {
                return AccessFilter(param.UserId, param.UserName, param.Password, timestmp, param.UserHostAddress, param.AccessOrigin);
            }
            return AccessFilter(param.UserId, param.UserName, param.Password, param.UserHostAddress, param.AccessOrigin);
        }
        
        /// <summary>
        /// 平台访问验证
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="userName"></param>
        /// <param name="secret"></param>
        /// <param name="timeStamp"></param>
        /// <param name="userHostAddress"></param>
        /// <param name="accessOrigin"></param>
        /// <returns></returns>
        public static Result AccessFilter(int userId, string userName, string secret, long timeStamp, string userHostAddress, AccessOrigin accessOrigin)
        {
            UserInfo userInfo = null;
            if (userId <= 0)
            {
                return Result.NewResult(StatusCode.UserIdError);
            }
            if (string.IsNullOrEmpty(userName))
            {
                return Result.NewResult(StatusCode.UserNameEmpty);
            }
            if (string.IsNullOrWhiteSpace(secret))
            {
                return Result.NewResult(StatusCode.SecretEmpty);
            }
            userInfo = DataCache.GetUserInfo(userId);
            if (userInfo == null)
            {
                return Result.NewResult(StatusCode.UserIdError);
            }
            if (!userInfo.UserName.Equals(userName))
            {
                return Result.NewResult(StatusCode.UserNameError);
            }
            if (timeStamp < 1)
            {
                return Result.NewResult(StatusCode.StampEmpty);
            }
            DateTime submitTime;
            try
            {
                submitTime = Utils.GetDateTime(timeStamp);
            }
            catch (Exception)
            {
                return Result.NewResult(StatusCode.StampError);
            }
            var total = Utils.GetTimeStamp(DateTime.Now) - timeStamp;
            if (Math.Abs(total) > 60 * 5)
            {
                return Result.NewResult(StatusCode.StampError);
            }
            var n_secret = Utils.Md5Encrypt(userInfo.Token + timeStamp).ToLower();
            if (n_secret != secret.ToLower())
            {
                return Result.NewResult(StatusCode.SecretError);
            }
            if (userInfo.Status != UserStatus.Enabled)
            {
                return Result.NewResult(StatusCode.UserStop);
            }
            //if (string.IsNullOrWhiteSpace(userHostAddress))
            //{
            //    return Result.NewResult(StatusCode.BindIPError);
            //}
            //if (!string.IsNullOrWhiteSpace(userInfo.UserHostAddress) && !userInfo.UserHostAddress.Contains(userHostAddress))
            //{
            //    return Result.NewResult(StatusCode.BindIPError);
            //}
            if (accessOrigin == AccessOrigin.Unknown)
            {
                return Result.NewResult(StatusCode.AccessRefuse);
            }
            if ((userInfo.AccessOrigin & accessOrigin) != accessOrigin)
            {
                return Result.NewResult(StatusCode.AccessRefuse);
            }
            return Result.NewResult(StatusCode.Success);
        }

        /// <summary>
        /// 接口访问验证
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="userName"></param>
        /// <param name="password"></param>
        /// <param name="userHostAddress"></param>
        /// <param name="accessOrigin"></param>
        /// <returns></returns>
        public static Result AccessFilter(int userId, string userName, string password, string userHostAddress, AccessOrigin accessOrigin)
        {
            UserInfo userInfo = null;
            if (userId <= 0)
            {
                return Result.NewResult(StatusCode.UserIdError);
            }
            if (string.IsNullOrEmpty(userName))
            {
                return Result.NewResult(StatusCode.UserNameEmpty);
            }
            if (string.IsNullOrWhiteSpace(password))
            {
                return Result.NewResult(StatusCode.PasswordEmpty);
            }
            userInfo = DataCache.GetUserInfo(userId);
            if (userInfo == null)
            {
                return Result.NewResult(StatusCode.UserIdError);
            }
            if (!userInfo.UserName.Equals(userName))
            {
                return Result.NewResult(StatusCode.UserNameError);
            }
            var md5Pwd = Common.Utils.Md5Encrypt16(password).ToLower();
            if (!userInfo.Token.ToLower().Equals(md5Pwd))
            {
                return Result.NewResult(StatusCode.PasswordError);
            }
            if (userInfo.Status != UserStatus.Enabled)
            {
                return Result.NewResult(StatusCode.UserStop);
            }
            if (string.IsNullOrWhiteSpace(userHostAddress))
            {
                return Result.NewResult(StatusCode.BindIPError);
            }
            if (!string.IsNullOrWhiteSpace(userInfo.UserHostAddress) && !userInfo.UserHostAddress.Contains(userHostAddress))
            {
                return Result.NewResult(StatusCode.BindIPError);
            }
            if (accessOrigin == AccessOrigin.Unknown)
            {
                return Result.NewResult(StatusCode.AccessRefuse);
            }
            if ((userInfo.AccessOrigin & accessOrigin) != accessOrigin)
            {
                return Result.NewResult(StatusCode.AccessRefuse);
            }
            return Result.NewResult(StatusCode.Success);
        }

        /// <summary>
        /// 对账户进行计费操作
        /// </summary>
        /// <param name="userId">账户Id</param>
        /// <param name="amountType">额度类型</param>
        /// <param name="serviceType">业务类型</param>
        /// <param name="billingCount">计费条数</param>
        /// <param name="billingAmount">计费金额</param>
        /// <param name="taskId">任务批次</param>
        /// <param name="amount">计费后的额度</param>
        /// <returns>操作状态</returns>
        private static StatusCode TryConsume(int userId, PayType payType, ServiceType serviceType, Amount billingCount, Amount billingAmount, string taskId, out Amount amount)
        {
            amount = 0;
            try
            {
                if (payType == PayType.Activity)
                {
                    return BillingCenter.Consume(userId, serviceType.GetAmountType(), serviceType, billingCount, taskId, out amount);
                }
                if (payType == PayType.Quota)
                {
                    return BillingCenter.Consume(userId, AmountType.Amount, serviceType, billingAmount, taskId, out amount);
                }
            }
            catch (Exception ex)
            {
                LogHelper.Error("计费失败，ex={0}", ex);
            }
            return StatusCode.ConsumeError;
        }
        /// <summary>
        /// 表示<see cref="InterfaceObject"/>对象的消息队列路径
        /// </summary>
        private static string InterfaceQueuePath
        {
            get { return App.GetSetting("InterfaceQueuePath"); }
        }
        /// <summary>
        /// 延迟加载消息队列池
        /// </summary>
        private static Lazy<MessageQueue> queue1 = new Lazy<MessageQueue>(() =>
        {
            string path = InterfaceQueuePath;
            if (string.IsNullOrEmpty(path))
            {
                path = @".\private$\YLHT_GP_Core_Interface";
            }
            return Utils.GetMessageQueue(path, new XmlMessageFormatter(new[] { typeof(InterfaceObject) }));
        });

        /// <summary>
        /// 获取消息队列对象
        /// </summary>
        private static MessageQueue Queue1
        {
            get
            {
                return queue1.Value;
            }
        }
        /// <summary>
        /// 将接口对象发送到消息队列
        /// </summary>
        /// <param name="interfaceObject"></param>
        private static void SendInterfaceObject(InterfaceObject interfaceObject)
        {
            Queue1.SendObject(interfaceObject, (MessagePriority)(int)interfaceObject.Priority);//当前先使用消息队列得优先级功能
        }
    }
}