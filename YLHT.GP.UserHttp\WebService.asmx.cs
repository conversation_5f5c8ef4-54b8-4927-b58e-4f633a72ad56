﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Services;
using YLHT.GP.Common;
using YLHT.GP.Common.Enums;
using YLHT.GP.Common.Objects.UserHttp;
using YLHT.GP.UserHttp.Objects;
using YLHT.GP.UserHttp.Processor;

namespace YLHT.GP.UserHttp
{
    /// <summary>
    /// WebService 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://localhost/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    [System.ComponentModel.ToolboxItem(false)]
    // 若要允许使用 ASP.NET AJAX 从脚本中调用此 Web 服务，请取消注释以下行。 
    // [System.Web.Script.Services.ScriptService]
    public class WebService : System.Web.Services.WebService
    {
        /// <summary>
        /// 设置当前的请求方式位WebService。
        /// </summary>
        private const AccessOrigin accessOrigin = AccessOrigin.WebService;

        /// <summary>
        /// 发送短信
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="account"></param>
        /// <param name="password"></param>
        /// <param name="mobile"></param>
        /// <param name="content"></param>
        /// <param name="planTime"></param>
        /// <param name="extnumber"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        [WebMethod]
        public SendResult SendSms(int userId,string account,string password,string mobile,string content,DateTime? planTime,string extnumber,string taskId)
        {
            Common.Objects.UserHttp.SmsParam param = new Common.Objects.UserHttp.SmsParam(userId, account, password, mobile, content, accessOrigin)
            {
                ExtNumber = extnumber,
                UserTaskId = taskId,
                UserHostAddress = Utils.GetRealIP(),
                SubmitTime = DateTime.Now,
                PlanTime = planTime
            };
            return InterfaceHandler.SmsHandler(param);
        }
    }
}
