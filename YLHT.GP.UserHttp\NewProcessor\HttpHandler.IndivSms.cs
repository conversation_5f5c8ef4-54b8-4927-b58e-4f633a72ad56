﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Web;
using YLHT.GP.Common;
using YLHT.GP.Common.Enums;
using YLHT.GP.Common.Log;
using YLHT.GP.Common.Modes;
using YLHT.GP.Common.Objects;
using YLHT.GP.Common.Objetcs20;
using YLHT.GP.UserHttp.Objects;

namespace YLHT.GP.UserHttp.NewProcessor
{
	public partial class HttpHandler
	{
        /// <summary>
        /// 发送个性短信
        /// </summary>
        /// <param name="param"></param>
        /// <param name="timestamp"></param>
        /// <param name="platform"></param>
        /// <returns></returns>
        public static IndivSendResult SendIndivSms(SmsParam param, long timestamp, bool platform)
        {
            Stopwatch watch = Stopwatch.StartNew();
            //NResult result = NResult.NewResult(StatusCode.Unknown);
            IndivSendResult result = new IndivSendResult(StatusCode.Unknown);
            try
            {
                var r = AccessFilter(param, timestamp, platform);
                if (!r)
                {
                    result = new IndivSendResult(r.StatusCode);
                    return result;
                }
                param.TaskId = GeneratorId.CreateTaskId();
                result = SendIndivSms(param);
            }
            catch (Exception ex)
            {
                LogHelper.Error("SendIndivSms,ex={0}", ex);
                result = new IndivSendResult(StatusCode.InternalError);
            }
            finally
            {
                watch.Stop();
                LogHelper.Info("发送个性短信,{0},watch={1},{2}", result, watch.ElapsedMilliseconds, param);
            }
            return result;
        }
        /// <summary>
        /// 发送个性短信
        /// </summary>
        /// <param name="param"></param>
        /// <returns></returns>
        private static IndivSendResult SendIndivSms(SmsParam param)
        {
            if (param.IndivLines == null || param.IndivLines.Count <= 0)
            {
                return new IndivSendResult(StatusCode.TextEmpty);
            }
            UserInfo userInfo = DataCache.GetUserInfo(param.UserId);
            param.UserPrice = userInfo.SmsPrice;
            param.ChargeType = userInfo.ChargeType;

            //允许发送时间
            if (userInfo.IsCts == 1)
            {
                var hour = DateTime.Now.Hour;
                if (!(userInfo.CtsStartTime == 0 && userInfo.CtsEndTime == 0))
                {
                    if (hour < userInfo.CtsStartTime)
                    {
                        return new IndivSendResult(StatusCode.CtsSend);
                    }
                    if (hour >= userInfo.CtsEndTime)
                    {
                        return new IndivSendResult(StatusCode.CtsSend);
                    }
                }
            }

            #region 用户扩展

            param.Extnumber = userInfo.ExtNumber + param.Extnumber;//将扩展号追加到用户的扩展后边

            #endregion


            #region 通道组验证

            int productId = DataCache.GetUserProduct(userInfo.UserId, param.ServiceType);
            if (productId < 1)
            {
                return new IndivSendResult(StatusCode.NoProduct);
            }
            param.ProductId = productId;

            #endregion

            #region 通道验证
            int[] channelIds = DataCache.GetProductChannel(productId);

            if (channelIds == null || channelIds.Length < 1)
            {
                return new IndivSendResult(StatusCode.NoChannel);
            }

            param.ChannelIds = channelIds;

			#endregion
			#region 号码参数等处理
			var i = 0;
            //存储手机号
            var msisdns =new List<string>();
            //错误的手机号
            var errorMsisdns = new List<string>();
            //存储正常短信、计费信息集合
            var msisdnList = new List<IndivSmsMsisdnItem>();
            foreach (var indiv in param.IndivLines)
            {
                i++;
                var line = indiv;
                #region 参数验证
                if (line.Length<12)//信息不完整
                {
                    errorMsisdns.Add($"{line},{i}行，信息不完整，已忽略");
                    continue;
                }
                if (!Utils.IsEmptyOrNumber(param.Extnumber))
                {
                    return new IndivSendResult(StatusCode.ExtnumberError,"扩展错误");
                }
                //获取手机号
                line = line.TrimStart(null);
                var msisdn = line.Substring(0, 11);
                if (MessageUtils.IsPhoneNumber(msisdn)==false)//错误号码
                {
                    errorMsisdns.Add($"{msisdn},{i}行，错误号码");
                    continue;
                }
                //验证手机号段
                if (!DataCache.TryGetMsisdnItem(msisdn,out MsisdnItem msisdnItem))
                {
                    errorMsisdns.Add($"{msisdn},{i}行，未识别号段");
                    continue;
                }
                
                //验证是否重复号码
                if (msisdns.Contains(msisdn))
                {
                    return new IndivSendResult(StatusCode.MsisdnRepeat,"手机号重复");
                }
                msisdns.Add(msisdn);
                //获取内容
                string text = line.Substring(11).TrimStart(new[] { ',', '，' }).Trim(); 
                
                if (text.Length > 2000)
                {
                    return new IndivSendResult(StatusCode.TextToLong,"内容太长");
                }

                #endregion

                #region 签名验证
                var sign = text.GetSign('【', '】');
                if (userInfo.SignVerify == SignVerify.Constraint)
                {
                    if (string.IsNullOrWhiteSpace(userInfo.ConstraintSign))
                    {
                        return new IndivSendResult(StatusCode.SignConstraintError,$"{i}行，强制签名为空");
                    }
                    if (userInfo.ConstraintSign != sign)
                    {
                        text += userInfo.ConstraintSign; ;
                    }
                }
                else if (userInfo.SignVerify == SignVerify.Reserved)
                {
                    string[] reservedSigns = DataCache.GetUserSigns(userInfo.UserId);
                    if (reservedSigns.Contains(sign) == false)
                    {
                        return new IndivSendResult(StatusCode.SignError,$"{i}行，签名错误，未报备签名");
                    }
                }
                else if (userInfo.SignVerify == SignVerify.Need)
                {
                    if (string.IsNullOrWhiteSpace(sign))
                    {
                        return new IndivSendResult(StatusCode.SignLack,$"{i}行，需要签名");
                    }
                }
                #endregion

                #region 敏感词验证
                if (userInfo.UseFailedKeyWord)
                {
                    var keyword = DataCache.GetKeyWords(0);

                    foreach (var item in keyword)
                    {
                        if (item.Match(text))
                        {
                            return new IndivSendResult(StatusCode.KeyWord, $"{i},行，敏感词：{item.Text}");
                        }
                    }

                    keyword = DataCache.GetKeyWords(param.UserId);

                    foreach (var item in keyword)
                    {
                        if (item.Match(text))
                        {
                            return new IndivSendResult(StatusCode.KeyWord, $"{i},行，敏感词：{item.Text}" );
                        }
                    }
                }
                #endregion

                List<string> messageList = userInfo.SplitMessage(param.ServiceType, text, out int billingCount);
                decimal billingAmount =  billingCount * param.UserPrice;
                var msidnItem = new IndivSmsMsisdnItem(msisdnItem)
                {
                    BillingAmount = billingAmount,
                    Content = text,
                    SmsContents = messageList,
                    BillingCount = billingCount,
                    TaskId= GeneratorId.CreateTaskId()
            };

                msisdnList.Add(msidnItem);
            }
			#endregion
			//该扣费的金额
			var billingAmount1 = msisdnList.Select(x => x.BillingAmount).Sum();
            var billingCount1 = msisdnList.Select(x=>x.BillingCount).Sum();
            #region 对额度进行初步验证

            if (userInfo.FeeType != FeeType.PostPaid)
            {
                var r = BillingCenter.GetAmountInfo(param.UserId, AmountType.Sms);

                if (r.Amount < msisdnList.Count)
                {
                    return new IndivSendResult(StatusCode.NoAmountCount);
                }
            }

            #endregion
            #region 扣费

            var code = TryConsume(param.UserId, param.PayType, param.ServiceType, billingCount1, billingAmount1, param.TaskId, out Amount amount);
            if (code != StatusCode.Success)
            {
                return new IndivSendResult(code);
            }

            #endregion


            #region 发送队列
            foreach (var item in msisdnList)
            {
                InterfaceObject20 interfaceObject20 = new InterfaceObject20();
                param.CopyTo(interfaceObject20);
                interfaceObject20.TaskId = item.TaskId;
                interfaceObject20.Text = item.Content;
                List<MsisdnItem> normalList = item.CopyTo();
                InterfaceObject interfaceObject = interfaceObject20.ToInterfaceObject(userInfo.AdminId, normalList, item.BillingCount, item.BillingAmount, item.SmsContents);
                interfaceObject.SendType= SendType.Single;
                
                SendInterfaceObject(interfaceObject);
            }
            LogHelper.Info($"发送队列完成，TaskId={param.TaskId}，ChildTaskIds={msisdnList.Select(z=>z.TaskId).Join(",")}");
            #endregion
            Counters.WriteSmsCounter(msisdnList.Count);
            var result = new IndivSendResult(StatusCode.Success) { 
                BillingCount = billingCount1,
                BillingAmount = billingAmount1,
                Amount = amount,
                SuccessCount = msisdnList.Count,
                ErrorList1 = errorMsisdns,
                FeeType = userInfo.FeeType,
                PayType = param.PayType
            };
            result.TaskId = param.TaskId;
            return result;
        }
    }
}