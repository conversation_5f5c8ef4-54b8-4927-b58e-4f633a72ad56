﻿<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{BEEE0FF1-ABE2-4712-BF46-4BA02A5FE8EB}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>YLHT.GP.UserHttp</RootNamespace>
    <AssemblyName>YLHT.GP.UserHttp</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <UseIISExpress>false</UseIISExpress>
    <Use64BitIISExpress>true</Use64BitIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System.Messaging" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="NewProcessor\HttpHandler.IndivSms.cs" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="callApi.aspx" />
    <Content Include="EventOperation.asmx" />
    <Content Include="Global.asax" />
    <Content Include="InternalService.asmx" />
    <Content Include="mms.aspx" />
    <Content Include="sms.aspx" />
    <Content Include="statusApi.aspx" />
    <Content Include="Web.config" />
    <Content Include="WebService.asmx" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="callApi.aspx.cs">
      <DependentUpon>callApi.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="callApi.aspx.designer.cs">
      <DependentUpon>callApi.aspx</DependentUpon>
    </Compile>
    <Compile Include="EventOperation.asmx.cs">
      <DependentUpon>EventOperation.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="InternalService.asmx.cs">
      <DependentUpon>InternalService.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="mms.aspx.cs">
      <DependentUpon>mms.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="mms.aspx.designer.cs">
      <DependentUpon>mms.aspx</DependentUpon>
    </Compile>
    <Compile Include="NewProcessor\Consts.cs" />
    <Compile Include="NewProcessor\HttpHandler.cs" />
    <Compile Include="NewProcessor\HttpHandler.Mms.cs" />
    <Compile Include="NewProcessor\HttpHandler.Sms.cs" />
    <Compile Include="NewProcessor\HttpServiceXml.cs" />
    <Compile Include="Objects\AmountParam.cs" />
    <Compile Include="Objects\IndivSmsMsisdnItem.cs" />
    <Compile Include="Objects\MmsParam.cs" />
    <Compile Include="Objects\MoParam.cs" />
    <Compile Include="Objects\MsgParam.cs" />
    <Compile Include="Objects\NResult.cs" />
    <Compile Include="Objects\ReportParam.cs" />
    <Compile Include="Objects\SaveMmsParam.cs" />
    <Compile Include="Objects\SaveMmsReportParam.cs" />
    <Compile Include="Objects\SendResult.cs" />
    <Compile Include="Objects\SmsParam.cs" />
    <Compile Include="Processor\Consts.cs" />
    <Compile Include="Counters.cs" />
    <Compile Include="Processor\HttpServiceXml.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Processor\InterfaceHandler.cs" />
    <Compile Include="Processor\InterfaceHandler.Methods.cs">
      <DependentUpon>InterfaceHandler.cs</DependentUpon>
    </Compile>
    <Compile Include="Processor\InterfaceHandler.Sms.cs">
      <DependentUpon>InterfaceHandler.cs</DependentUpon>
    </Compile>
    <Compile Include="Processor\InterfaceHandler.Test.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="sms.aspx.cs">
      <DependentUpon>sms.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="sms.aspx.designer.cs">
      <DependentUpon>sms.aspx</DependentUpon>
    </Compile>
    <Compile Include="statusApi.aspx.cs">
      <DependentUpon>statusApi.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="statusApi.aspx.designer.cs">
      <DependentUpon>statusApi.aspx</DependentUpon>
    </Compile>
    <Compile Include="WebService.asmx.cs">
      <DependentUpon>WebService.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\YLHT.GP.Common\YLHT.GP.Common.csproj">
      <Project>{0B1FA38B-8830-4070-9D5F-6D4E8E95DCC1}</Project>
      <Name>YLHT.GP.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\YLHT.GP.OracleData\YLHT.GP.OracleData.csproj">
      <Project>{727c2adb-f3cb-49d7-987a-d731e3d5f8d4}</Project>
      <Name>YLHT.GP.OracleData</Name>
    </ProjectReference>
    <ProjectReference Include="..\YLHT.GP.Redis\YLHT.GP.Redis.csproj">
      <Project>{dcdfa800-df25-44b9-9258-ea022e14917d}</Project>
      <Name>YLHT.GP.Redis</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <SaveServerSettingsInUserFile>True</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>