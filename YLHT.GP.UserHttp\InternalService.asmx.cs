﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Services;
using YLHT.GP.Common;
using YLHT.GP.Common.Enums;
using YLHT.GP.Common.Host;
using YLHT.GP.Common.Log;
using YLHT.GP.Common.Objects.UserHttp;
using YLHT.GP.UserHttp.NewProcessor;
using YLHT.GP.UserHttp.Objects;
using YLHT.GP.UserHttp.Processor;
using SmsParam = YLHT.GP.Common.Objects.UserHttp.SmsParam;
using SmsVoiceParam = YLHT.GP.Common.Objects.UserHttp.SmsVoiceParam;
using SmsParam20 = YLHT.GP.UserHttp.Objects.SmsParam;

namespace YLHT.GP.UserHttp
{
    /// <summary>
    /// InternalService 的摘要说明
    /// </summary>
    [WebService(Namespace = "http://localhost/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    [System.ComponentModel.ToolboxItem(false)]
    // 若要允许使用 ASP.NET AJAX 从脚本中调用此 Web 服务，请取消注释以下行。 
    // [System.Web.Script.Services.ScriptService]
    public class InternalService : System.Web.Services.WebService
    {
        /// <summary>
        /// 设置当前的请求方式为平台提交
        /// </summary>
        private const AccessOrigin accessOrigin = AccessOrigin.Platform;

        /// <summary>
        /// 仅支持以下ip的请求
        /// </summary>
        private readonly static string BindIP = App.GetSetting("BindIP");

        /// <summary>
        /// 发送短信接口，仅内部使用
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="account"></param>
        /// <param name="secret"></param>
        /// <param name="timestamp"></param>
        /// <param name="mobile"></param>
        /// <param name="content"></param>
        /// <param name="planTime"></param>
        /// <param name="extnumber"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        [WebMethod]
        [Obsolete("SendSmsByServiceType")]
        public SendResult SendSms(int userId, string account, string secret, long timestamp, string mobile, string content, DateTime? planTime, string extnumber, string taskId)
        {
            string userHostAddress = Utils.GetRealIP();

            if (string.IsNullOrEmpty(BindIP)==false && BindIP.Contains(userHostAddress)==false)
            {
                return new SendResult(StatusCode.BindIPError);
            }

            SmsParam param = new SmsParam(userId, account, secret, mobile, content, accessOrigin)
            {
                ExtNumber = extnumber,
                UserTaskId = taskId,
                UserHostAddress = userHostAddress,
                SubmitTime = DateTime.Now,
                PlanTime=planTime
            };
            return InterfaceHandler.InternalSmsHandler(param,timestamp);
        }

        /// <summary>
        /// 通道测试
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="account"></param>
        /// <param name="secret"></param>
        /// <param name="timestamp"></param>
        /// <param name="mobile"></param>
        /// <param name="content"></param>
        /// <param name="extnumber"></param>
        /// <param name="channelId"></param>
        /// <returns></returns>
        [WebMethod]
        public SendResult SendSmsByChannel(int userId, string account, string secret, long timestamp, string mobile, string content, string extnumber, int channelId)
        {
            string userHostAddress = Utils.GetRealIP();
            if (string.IsNullOrEmpty(BindIP) == false && BindIP.Contains(userHostAddress) == false)
            {
                return new SendResult(StatusCode.BindIPError);
            }
            VerifyParam param = new VerifyParam();
            param.UserId = userId;
            param.Account = account;
            param.Timestamp = timestamp;
            param.Secret = secret;
            param.UserHostAddress = userHostAddress;

            SmsByChannelParam param1 = new SmsByChannelParam();
            param1.AdminId = userId;
            param1.ChannelId = channelId;
            param1.Msisdn =mobile;
            param1.Text = content;
            param1.ExtNumber = extnumber;
            param1.AccessOrigin = AccessOrigin.ChannelTest;
            param1.UserHostAddress = userHostAddress;
            param1.SubmitTime = DateTime.Now;

            return InterfaceHandler.SendSmsByChannel(param, param1);
        }

        /// <summary>
        /// 通道测试1
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="account"></param>
        /// <param name="secret"></param>
        /// <param name="timestamp"></param>
        /// <param name="mobile"></param>
        /// <param name="content"></param>
        /// <param name="extnumber"></param>
        /// <param name="channelId"></param>
        /// <param name="serviceType"></param>
        /// <returns></returns>
        [WebMethod]
        public SendResult SendSmsByChannel1(int userId, string account, string secret, long timestamp, string mobile, string content, string extnumber, int channelId, ServiceType serviceType)
        {
            string userHostAddress = Utils.GetRealIP();
            if (string.IsNullOrEmpty(BindIP) == false && BindIP.Contains(userHostAddress) == false)
            {
                return new SendResult(StatusCode.BindIPError);
            }
            if (serviceType != ServiceType.Sms && serviceType != ServiceType.Fms)
            {
                return new SendResult(StatusCode.ServiceTypeError);
            }

            VerifyParam param = new VerifyParam();
            param.UserId = userId;
            param.Account = account;
            param.Timestamp = timestamp;
            param.Secret = secret;
            param.UserHostAddress = userHostAddress;

            SmsByChannelParam param1 = new SmsByChannelParam();
            param1.AdminId = userId;
            param1.ChannelId = channelId;
            param1.Msisdn = mobile;
            param1.Text = content;
            param1.ExtNumber = extnumber;
            param1.AccessOrigin = AccessOrigin.ChannelTest;
            param1.UserHostAddress = userHostAddress;
            param1.SubmitTime = DateTime.Now;
            param1.ServiceType = serviceType;

            return InterfaceHandler.SendSmsByChannel(param, param1);
        }

        /// <summary>
        /// 发送短信接口，仅内部使用
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="account"></param>
        /// <param name="secret"></param>
        /// <param name="timestamp"></param>
        /// <param name="mobile"></param>
        /// <param name="content"></param>
        /// <param name="planTime"></param>
        /// <param name="extnumber"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        [WebMethod]
        [Obsolete("SendSmsByServiceType")]
        public NResult SendSms1(int userId, string account, string secret, long timestamp, string mobile, string content, DateTime? planTime, string extnumber, string taskId)
        {
            string userHostAddress = Utils.GetRealIP();

            if (string.IsNullOrEmpty(BindIP) == false && BindIP.Contains(userHostAddress) == false)
            {
                return NResult.NewResult(StatusCode.BindIPError);
            }
            SmsParam20 param = new SmsParam20()
            {
                UserId = userId,
                UserName = account,
                Password = secret,
                UserHostAddress = userHostAddress,
                AccessOrigin = accessOrigin,
                Msisdn = mobile,
                Text=content,
                Extnumber = extnumber,
                PlanTime = planTime,
                SubmitTime = DateTime.Now
            };
            return HttpHandler.SendSms(param, timestamp, true);
        }

        /// <summary>
        /// 发送短信接口，仅内部使用
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="account"></param>
        /// <param name="secret"></param>
        /// <param name="timestamp"></param>
        /// <param name="mobile"></param>
        /// <param name="content"></param>
        /// <param name="planTime"></param>
        /// <param name="extnumber"></param>
        /// <param name="taskId"></param>
        /// <param name="serviceType"></param>
        /// <returns></returns>
        [WebMethod]
        public NResult SendSmsByServiceType(int userId, string account, string secret, long timestamp, string mobile, string content, DateTime? planTime, string extnumber, string taskId,ServiceType serviceType)
        {
            string userHostAddress = Utils.GetRealIP();

            if (string.IsNullOrEmpty(BindIP) == false && BindIP.Contains(userHostAddress) == false)
            {
                return NResult.NewResult(StatusCode.BindIPError);
            }
            if (serviceType != ServiceType.Sms && serviceType != ServiceType.Fms)//仅支持短信和闪信的发送
            {
                return NResult.NewResult(StatusCode.ServiceTypeError);
            }
            SmsParam20 param = new SmsParam20()
            {
                UserId = userId,
                UserName = account,
                Password = secret,
                UserHostAddress = userHostAddress,
                AccessOrigin = accessOrigin,
                Msisdn = mobile,
                Text = content,
                Extnumber = extnumber,
                PlanTime = planTime,
                SubmitTime = DateTime.Now
            };
            param.ServiceType = serviceType;
            param.TaskId = taskId;
            return HttpHandler.SendSms(param, timestamp, true);
        }
        /// <summary>
        /// 发送个性短信接口，仅内部使用
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="account"></param>
        /// <param name="secret"></param>
        /// <param name="timestamp"></param>
        /// <param name="buffer"></param>
        /// <param name="planTime"></param>
        /// <param name="extnumber"></param>
        /// <param name="taskId"></param>
        /// <param name="serviceType"></param>
        /// <returns></returns>
        [WebMethod]
        public IndivSendResult SendIndivSmsByServiceType(int userId, string account, string secret, long timestamp, byte[] buffer, DateTime? planTime, string extnumber, string taskId, ServiceType serviceType)
        {
            MemoryStream inStream = null;
            GZipStream zipStream = null;
            StreamReader reader = null;

            try
            {
                string userHostAddress = Utils.GetRealIP();

                if (string.IsNullOrEmpty(BindIP) == false && BindIP.Contains(userHostAddress) == false)
                {
                    return new IndivSendResult(StatusCode.BindIPError);
                }
                if (serviceType != ServiceType.Sms && serviceType != ServiceType.Fms)//仅支持短信和闪信的发送
                {
                    return new IndivSendResult(StatusCode.ServiceTypeError);
                }
                inStream = new MemoryStream(buffer);
                zipStream = new GZipStream(inStream, CompressionMode.Decompress);
                reader = new StreamReader(zipStream, Encoding.UTF8);
                SmsParam20 param = new SmsParam20()
                {
                    UserId = userId,
                    UserName = account,
                    Password = secret,
                    UserHostAddress = userHostAddress,
                    AccessOrigin = accessOrigin,
                    Extnumber = extnumber,
                    PlanTime = planTime,
                    IndivLines = reader.ToList(),
                    SubmitTime = DateTime.Now
                };
                param.ServiceType = serviceType;
                return HttpHandler.SendIndivSms(param, timestamp, true);
            }
            catch (Exception ex)
            {
                LogHelper.Error("SendIndivSmsByServiceType,ex="+ex);
                return new IndivSendResult(StatusCode.InternalError);
            }
            finally
            {
                if (inStream != null)
                    inStream.Dispose();
                if (zipStream != null)
                    zipStream.Dispose();
                if (reader != null)
                    reader.Dispose();
            }
        }
        /// <summary>
        /// 客户端彩信测试
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="userName"></param>
        /// <param name="secret"></param>
        /// <param name="timeStamp"></param>
        /// <param name="mobile"></param>
        /// <param name="subject"></param>
        /// <param name="content"></param>
        /// <param name="planTime"></param>
        /// <param name="extnumber"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        [WebMethod]
        public NResult SendMms(int userId, string userName, string secret, long timeStamp,string mobile,string subject,string content,DateTime? planTime,string extnumber,string taskId)
        {
            string userHostAddress = Utils.GetRealIP();
            if (string.IsNullOrEmpty(BindIP) == false && BindIP.Contains(userHostAddress) == false)
            {
                return  NResult.NewResult(StatusCode.BindIPError);
            }
            MmsParam param = new MmsParam()
            {
                UserId = userId,
                UserName = userName,
                Password = secret,
                UserHostAddress = userHostAddress,
                AccessOrigin = accessOrigin,
                Msisdn = mobile,
                Subject = subject,
                Content = content,
                Extnumber = extnumber,
                PlanTime=planTime,
                SubmitTime=DateTime.Now
            };
            return HttpHandler.PlatformSendMms(param, timeStamp);
        }

        /// <summary>
        /// 发送彩信根据彩信模板ID
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="userName"></param>
        /// <param name="secret"></param>
        /// <param name="timeStamp"></param>
        /// <param name="mobile"></param>
        /// <param name="mmsId"></param>
        /// <param name="planTime"></param>
        /// <param name="extnumber"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        [WebMethod]
        public NResult SendMmsByMmsId(int userId, string userName, string secret, long timeStamp, string mobile, string mmsId, DateTime? planTime, string extnumber, string taskId)
        {
            string userHostAddress = Utils.GetRealIP();
            if (string.IsNullOrEmpty(BindIP) == false && BindIP.Contains(userHostAddress) == false)
            {
                return NResult.NewResult(StatusCode.BindIPError);
            }
            MmsParam param = new MmsParam()
            {
                UserId = userId,
                UserName = userName,
                Password = secret,
                UserHostAddress = userHostAddress,
                AccessOrigin = accessOrigin,
                Msisdn = mobile,
                MmsId = mmsId,
                Extnumber = extnumber,
                PlanTime = planTime,
                SubmitTime = DateTime.Now
            };
            return HttpHandler.PlatformSendMmsByMmsId(param, timeStamp);
        }

        /// <summary>
        /// 发送彩信根据彩信模板ID带变量
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="userName"></param>
        /// <param name="secret"></param>
        /// <param name="timeStamp"></param>
        /// <param name="mobile"></param>
        /// <param name="mmsId"></param>
        /// <param name="planTime"></param>
        /// <param name="extnumber"></param>
        /// <param name="taskId"></param>
        /// <param name="vars"></param>
        /// <returns></returns>
        [WebMethod]
        public NResult SendMmsByMmsIdVars(int userId, string userName, string secret, long timeStamp, string mobile, string mmsId, DateTime? planTime, string extnumber, string taskId,string vars)
        {
            string userHostAddress = Utils.GetRealIP();
            if (string.IsNullOrEmpty(BindIP) == false && BindIP.Contains(userHostAddress) == false)
            {
                return NResult.NewResult(StatusCode.BindIPError);
            }
            MmsParam param = new MmsParam()
            {
                UserId = userId,
                UserName = userName,
                Password = secret,
                UserHostAddress = userHostAddress,
                AccessOrigin = accessOrigin,
                Msisdn = mobile,
                MmsId = mmsId,
                Extnumber = extnumber,
                PlanTime = planTime,
                SubmitTime = DateTime.Now,
                Vars = vars
            };
            return HttpHandler.PlatformSendMmsByMmsId(param, timeStamp);
        }


        /// <summary>
        /// 彩信报备
        /// </summary>
        /// <param name="userid"></param>
        /// <param name="userName"></param>
        /// <param name="secret"></param>
        /// <param name="timeStamp"></param>
        /// <param name="subject"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        [WebMethod]
        public SaveMmsResult SaveMms(int userid, string userName, string secret, long timeStamp, string subject, string content)
        {
            string userHostAddress = Utils.GetRealIP();
            if (string.IsNullOrEmpty(BindIP) == false && BindIP.Contains(userHostAddress) == false)
            {
                return new SaveMmsResult(StatusCode.BindIPError);
            }
            SaveMmsParam param = new SaveMmsParam {
                UserId=userid,
                UserName=userName,
                Password=secret,
                Subject=subject,
                Content=content,
                UserHostAddress=userHostAddress,
                AccessOrigin=accessOrigin
            };
            return HttpHandler.PlatformSaveMms(param, timeStamp);
        }

        [WebMethod]
        [Obsolete("SendVoiceSms")]
        public SendResult SendVoiceSms(int userId, string account, string secret, long timestamp, string mobile, DateTime? planTime, string taskId,int smsTemplateId,int voiceTemplateId,int triggerSms)
        {
            string userHostAddress = Utils.GetRealIP();

            if (string.IsNullOrEmpty(BindIP) == false && BindIP.Contains(userHostAddress) == false)
            {
                return new SendResult(StatusCode.BindIPError);
            }

            SmsVoiceParam param = new SmsVoiceParam(userId, account, secret, mobile, voiceTemplateId, accessOrigin)
            {
                UserTaskId = taskId,
                UserHostAddress = userHostAddress,
                SubmitTime = DateTime.Now,
                PlanTime = planTime,
                SmsTemplateId = smsTemplateId,
                TiggerSms=triggerSms
            };
            return InterfaceHandler.InternalVoiceSmsHandler(param, timestamp);
        }


    }
}
