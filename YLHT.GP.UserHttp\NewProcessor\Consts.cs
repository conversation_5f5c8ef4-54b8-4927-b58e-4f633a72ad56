﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace YLHT.GP.UserHttp.NewProcessor
{
    public static class Consts
    {
        /// <summary>
        /// 成功值
        /// </summary>
        public const string XmlSuccess = "Success";

        /// <summary>
        /// 失败值
        /// </summary>
        public const string XmlFaild = "Faild";

        /// <summary>
        /// 消息队列地址
        /// </summary>
        public const string InterfaceQueuePath20 = @".\private$\YLHT_GP_Core_Interface20";

        /// <summary>
        /// 错误格式
        /// </summary>
        public const string XmlErrorFormat = @"<?xml version=""1.0"" encoding=""{0}"" ?>
<returnsms>
  <errorstatus>
    <error>{1}</error>
    <remark>{2}</remark>
  </errorstatus>
</returnsms>";

        /// <summary>
        /// 获取余额格式
        /// </summary>
        public const string XmlAmountFormat = @"<?xml version=""1.0"" encoding=""{0}"" ?>
<returnsms>
  <returnstatus>{1}</returnstatus>
  <message>{2}</message>
  <payinfo>{3}</payinfo>
  <overage>{4}</overage>
  <sendTotal>{5}</sendTotal>
</returnsms>";

        /// <summary>
        /// 发送完成格式
        /// </summary>
        public const string XmlSendFormat = @"<?xml version=""1.0"" encoding=""{0}"" ?>
<returnsms>
    <returnstatus>{1}</returnstatus>
    <message>{2}</message>
    <taskID>{3}</taskID>
</returnsms>";

        /// <summary>
        /// 报备彩信返回格式
        /// </summary>
        public const string XmlSaveFormat = @"<?xml version=""1.0"" encoding=""{0}"" ?>
<returnsms>
    <returnstatus>{1}</returnstatus>
    <message>{2}</message>
    <mmsID>{3}</mmsID>
</returnsms>";

        /// <summary>
        /// 获取彩信模板状态格式
        /// </summary>
        public const string XmlGetSaveReportFormat = @"<?xml version=""1.0"" encoding=""{0}"" ?> 
<returnsms>{1}
</returnsms>";

        /// <summary>
        /// 获取彩信模板状态Box格式
        /// </summary>
        public const string xmlSaveReportBoxFormat = @"
    <statusbox>
        <mmsid>{0}</mmsid>
        <status>{1}</status>
    </statusbox>";

    }
}